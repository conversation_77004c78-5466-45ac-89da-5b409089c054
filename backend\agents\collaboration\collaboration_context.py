"""
Collaboration Context System for Multi-Agent Medical Chatbot

This module provides shared context and communication mechanisms for agent collaboration.
"""

from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from datetime import datetime
import json
import uuid


@dataclass
class AgentResult:
    """Result from an individual agent."""
    agent_name: str
    content: Any
    confidence: float
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    processing_time: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'agent_name': self.agent_name,
            'content': self.content,
            'confidence': self.confidence,
            'metadata': self.metadata,
            'timestamp': self.timestamp.isoformat(),
            'processing_time': self.processing_time
        }


@dataclass
class CollaborationStep:
    """Single step in a collaboration workflow."""
    step_id: str
    agent_name: str
    input_data: Any
    result: AgentResult
    dependencies: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        if not self.step_id:
            self.step_id = str(uuid.uuid4())


class CollaborationContext:
    """Shared context for agent collaboration."""
    
    def __init__(self, session_id: str = None):
        self.session_id = session_id or str(uuid.uuid4())
        self.steps: List[CollaborationStep] = []
        self.shared_data: Dict[str, Any] = {}
        self.agent_capabilities: Dict[str, List[str]] = {}
        self.collaboration_history: List[Dict] = []
        
    def add_step(self, agent_name: str, input_data: Any, result: AgentResult, 
                 dependencies: List[str] = None) -> str:
        """Add a collaboration step."""
        step = CollaborationStep(
            step_id=str(uuid.uuid4()),
            agent_name=agent_name,
            input_data=input_data,
            result=result,
            dependencies=dependencies or []
        )
        self.steps.append(step)
        return step.step_id
    
    def get_results_by_agent(self, agent_name: str) -> List[AgentResult]:
        """Get all results from a specific agent."""
        return [step.result for step in self.steps if step.agent_name == agent_name]
    
    def get_latest_result(self, agent_name: str) -> Optional[AgentResult]:
        """Get the latest result from a specific agent."""
        results = self.get_results_by_agent(agent_name)
        return results[-1] if results else None
    
    def set_shared_data(self, key: str, value: Any):
        """Set shared data accessible to all agents."""
        self.shared_data[key] = value
    
    def get_shared_data(self, key: str, default: Any = None) -> Any:
        """Get shared data."""
        return self.shared_data.get(key, default)
    
    def register_agent_capabilities(self, agent_name: str, capabilities: List[str]):
        """Register what an agent can do."""
        self.agent_capabilities[agent_name] = capabilities
    
    def find_capable_agents(self, required_capability: str) -> List[str]:
        """Find agents that have a specific capability."""
        capable_agents = []
        for agent_name, capabilities in self.agent_capabilities.items():
            if required_capability in capabilities:
                capable_agents.append(agent_name)
        return capable_agents
    
    def get_collaboration_summary(self) -> Dict[str, Any]:
        """Get summary of the collaboration session."""
        return {
            'session_id': self.session_id,
            'total_steps': len(self.steps),
            'agents_involved': list(set(step.agent_name for step in self.steps)),
            'total_processing_time': sum(step.result.processing_time for step in self.steps),
            'average_confidence': sum(step.result.confidence for step in self.steps) / len(self.steps) if self.steps else 0,
            'shared_data_keys': list(self.shared_data.keys())
        }


class CollaborationOrchestrator:
    """Orchestrates collaboration between agents."""
    
    def __init__(self):
        self.active_contexts: Dict[str, CollaborationContext] = {}
        self.collaboration_patterns = {}
        
    def create_context(self, session_id: str = None) -> CollaborationContext:
        """Create a new collaboration context."""
        context = CollaborationContext(session_id)
        self.active_contexts[context.session_id] = context
        return context
    
    def get_context(self, session_id: str) -> Optional[CollaborationContext]:
        """Get existing collaboration context."""
        return self.active_contexts.get(session_id)
    
    def register_pattern(self, pattern_name: str, pattern_class):
        """Register a collaboration pattern."""
        self.collaboration_patterns[pattern_name] = pattern_class
    
    def execute_pattern(self, pattern_name: str, context: CollaborationContext, 
                       input_data: Any, **kwargs) -> Any:
        """Execute a specific collaboration pattern."""
        if pattern_name not in self.collaboration_patterns:
            raise ValueError(f"Unknown collaboration pattern: {pattern_name}")
        
        pattern_class = self.collaboration_patterns[pattern_name]
        pattern_instance = pattern_class(context, **kwargs)
        return pattern_instance.execute(input_data)


# Global orchestrator instance
collaboration_orchestrator = CollaborationOrchestrator()
